{"name": "tempo", "version": "0.0.1", "license": "MIT", "bin": "dist/cli.js", "type": "module", "engines": {"node": "22.17.1", "pnpm": "10.13.1"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "ava", "start": "node dist/cli.js", "lint": "pnpm exec biome lint", "lint:fix": "pnpm exec biome lint --write", "prepare": "husky"}, "files": ["dist"], "dependencies": {"@inkjs/ui": "^2.0.0", "chalk": "^5.4.1", "ink": "^6.0.1", "meow": "^13.2.0", "react": "^19.1.0"}, "devDependencies": {"@biomejs/biome": "2.1.2", "@tsconfig/strictest": "^2.0.5", "@types/node": "^24.0.7", "@types/react": "^19.1.8", "ava": "^6.4.0", "husky": "^9.1.7", "ink-testing-library": "^4.0.0", "lint-staged": "^16.1.2", "ts-node": "^10.9.2", "typescript": "^5.8.3", "ultracite": "5.0.46"}, "ava": {"extensions": {"ts": "module", "tsx": "module"}, "nodeArguments": ["--loader=ts-node/esm"]}, "packageManager": "pnpm@10.13.1+sha512.37ebf1a5c7a30d5fabe0c5df44ee8da4c965ca0c5af3dbab28c3a1681b70a256218d05c81c9c0dcf767ef6b8551eb5b960042b9ed4300c59242336377e01cfad", "lint-staged": {"*.{js,jsx,ts,tsx,json,jsonc,css,scss}": ["pnpm exec ultracite format"]}}